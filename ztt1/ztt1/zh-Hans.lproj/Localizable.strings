/* 
 * Localizable.strings (Chinese Simplified)
 * ztt1
 * 
 * 团团转应用中文本地化文件
 */

// MARK: - 通用按钮文本
"common.button.cancel" = "取消";
"common.button.confirm" = "确定";
"common.button.delete" = "删除";
"common.button.save" = "保存";
"common.button.close" = "关闭";

// MARK: - 主页操作按钮
"home.button.family_total_score" = "全家一共加分";
"home.button.add_member" = "添加成员";
"home.button.family_operation" = "全家操作";
"member_grid.search_placeholder" = "搜索成员姓名或...";

// MARK: - 成员管理
"member.empty.title" = "暂无成员";
"member.empty.description" = "点击\"添加成员\"按钮开始添加成员";
"member.delete.title" = "删除成员";
"member.delete.message" = "确定要删除成员 \"%@\" 吗？";
"member.delete.confirm" = "确定删除";
"member.info.unknown_name" = "未知";
"member.info.default_number" = "000";
"member.info.family_info" = "我的家庭";

// MARK: - 成长日记页面
"growth_diary.title" = "成长日记";
"growth_diary.input.placeholder" = "记录今天的成长点滴...";
"growth_diary.button.save" = "保存日记";
"growth_diary.button.view_history" = "查看历史日记";
"growth_diary.date.label" = "记录时间";
"growth_diary.save.success" = "日记保存成功";
"growth_diary.save.error" = "保存失败，请重试";
"growth_diary.input.empty" = "请输入日记内容";

// 选择对象功能
"growth_diary.member.label" = "选择对象";
"growth_diary.member.select" = "选择孩子";
"growth_diary.member.title" = "选择记录对象";
"growth_diary.member.empty" = "还没有添加孩子\n请先在首页添加家庭成员";

// 弹窗功能
"growth_diary.date.picker.title" = "选择日期";
"growth_diary.member.picker.title" = "选择孩子";

// 历史日记查看功能
"growth_diary.history.title" = "选择查看对象";
"growth_diary.history.empty" = "还没有添加孩子\n无法查看历史日记";
"growth_diary.report.analysis" = "分析报告";
"growth_diary.report.growth" = "成长报告";
"growth_diary.report.analysis.title" = "AI分析报告";
"growth_diary.report.analysis.description" = "基于积分记录和行为数据\n生成专业的分析报告";
"growth_diary.report.growth.title" = "成长报告";
"growth_diary.report.growth.description" = "基于日记内容和情绪分析\n生成个性化的成长建议";

// MARK: - 设置页面
"settings.title" = "设置";
"settings.class_management.title" = "班级管理";
"settings.class_management.create_button" = "创建班级";
"settings.class_management.config_button" = "配置";
"settings.class_management.options_button" = "操作";
"settings.class_management.empty.title" = "暂无班级";
"settings.class_management.empty.description" = "点击右上角创建第一个班级";
"settings.class_management.student_count" = "%d 名学生";
"settings.class_management.frozen_tag" = "已冻结";

"settings.function_config.title" = "功能配置";
"settings.function_config.rule.title" = "规则库配置";
"settings.function_config.rule.description" = "配置常用的加分扣分规则";
"settings.function_config.reward.title" = "奖品库配置";
"settings.function_config.reward.description" = "配置兑换的奖品";
"settings.function_config.lottery.title" = "抽奖道具配置";
"settings.function_config.lottery.description" = "配置大转盘、盲盒和刮刮卡的奖品";

// MARK: - 个人中心
"profile.title" = "个人中心";
"profile.user_info.id_label" = "ID:%@";
"profile.user_info.membership_expires" = "会员到期时间：%@";
"profile.subscription.banner_text" = "升级会员，解锁更多专属权益";
"profile.subscription.view_plans_button" = "查看会员方案";
"profile.subscription.card_title" = "个人信息卡片";
"profile.no_email" = "未获取到邮箱";

// MARK: - 系统设置项
"settings.item.language" = "语言切换";
"settings.item.feedback" = "帮助与反馈";
"settings.item.about" = "关于";
"settings.item.user_agreement" = "用户协议";
"settings.item.privacy_policy" = "隐私政策";
"settings.item.children_privacy_policy" = "儿童个人信息保护政策";
"login.agreement.children_privacy_policy" = "《儿童个人隐私保护政策》";
"settings.item.delete_account" = "删除账号";
"settings.item.logout" = "退出登录";

// MARK: - 帮助与反馈
"feedback.contact_email.title" = "联系我们";
"feedback.contact_email.message" = "联系邮箱：<EMAIL>";
"feedback.contact_email.confirm" = "确定";

// MARK: - 退出登录
"logout.title" = "退出登录";
"logout.message" = "确定要退出登录吗？";
"logout.cancel" = "取消";
"logout.confirm" = "确认退出";

// MARK: - 学生详情页面
"student_detail.history.points" = "积分记录";
"student_detail.history.exchange" = "兑换记录";
"student_detail.history.empty.title" = "暂无记录";
"student_detail.history.empty.description" = "学生的积分变动记录将在这里显示";
"student_detail.actions.add_points" = "加分";
"student_detail.actions.deduct_points" = "扣分";
"student_detail.actions.exchange" = "兑换";
"student_detail.actions.lottery" = "抽奖";
"student_detail.actions.analysis" = "分析报告";

// MARK: - 订阅页面
"subscription.membership.basic" = "初级会员";
"subscription.membership.premium" = "高级会员";
"subscription.subscribe_button" = "立即订阅";
"subscription.agreement_notice" = "订阅会员前请阅读《会员服务协议》";
"subscription.agreement_prompt" = "请在订阅前阅读";
"subscription.agreement_link" = "《会员服务协议》";
"subscription.user_info.not_member" = "未开通会员，无法解锁全部功能";
"subscription.not_activated" = "未开通";

// MARK: - 导航和标签页
"tab.home" = "首页";
"tab.settings" = "成长日记";
"tab.profile" = "我的";
"tab.current_selection_format" = "当前选中: %d";

// MARK: - 占位页面文本
"placeholder.settings.title" = "设置页面";
"placeholder.profile.title" = "个人中心";
"placeholder.developing" = "功能开发中...";

// MARK: - 类选择器
"class_selector.preview" = "班级选择器预览";

// MARK: - 应用标题
"app.title" = "团团转";

// MARK: - 登录页面
"login.subtitle" = "专为家长打造的孩子积分管理工具";
"login.agreement.prefix" = "我已阅读并同意";
"login.agreement.user_agreement" = "《用户协议》";
"login.agreement.and" = "和";
"login.agreement.privacy_policy" = "《隐私政策》";
"login.error.title" = "登录错误";
"login.error.failed" = "登录失败，请重试";
"login.error.invalid_credential" = "无效的登录凭证";
"login.error.invalid_response" = "登录响应无效";
"login.error.not_handled" = "登录请求未处理";
"login.error.unknown" = "未知登录错误";
"login.error.general" = "登录时发生错误，请稍后重试";
"login.webview.placeholder" = "此处将显示网页内容";
"common.button.ok" = "确定";
"common.button.close" = "关闭";

// MARK: - 语言切换
"language.restart.title" = "重启应用";
"language.restart.message" = "语言切换需要重启应用才能生效，确定要继续吗？";
"language.selection.title" = "选择语言";
"language.selection.description" = "选择应用界面显示语言，切换后需要重启应用生效";

// MARK: - 日期范围选择
"date_range.this_week" = "本周";
"date_range.this_month" = "本月";
"date_range.custom" = "自定义";
"date_range.select_range" = "选择时间范围";
"date_range.start_date" = "开始日期";
"date_range.end_date" = "结束日期";
"date_range.invalid_range" = "结束日期不能早于开始日期";
"date_range.total_score_format" = "%@内全班一共加分";

// MARK: - 通用操作
"common.cancel" = "取消";
"common.confirm" = "确认";

// MARK: - 添加成员功能
"add_member.no_family.title" = "尚未创建家庭";
"add_member.no_family.message" = "您还没有创建任何家庭，无法添加成员";
"add_member.no_family.instruction" = "请先创建一个家庭，然后再来添加成员";
"add_member.no_family.create_hint" = "在设置页面可以创建家庭";
"add_member.no_family.create_button" = "去创建家庭";



"add_member.form.title" = "添加成员";
"add_member.form.member_number_label" = "成员 %@";
"add_member.form.name" = "姓名";
"add_member.form.name_placeholder" = "请输入成员姓名";
// 已移除的字段 - 不再使用
// "add_member.form.member_number" = "编号";
// "add_member.form.number_placeholder" = "请输入100以内数字";
// "add_member.form.gender" = "性别";
// "add_member.form.gender_male" = "男";
// "add_member.form.gender_female" = "女";
"add_member.form.initial_points" = "初始积分";
"add_member.form.role" = "角色";
"add_member.form.birth_date" = "出生日期";
"add_member.form.submit" = "添加成员";
"add_member.form.submitting" = "添加中...";



// MARK: - 成员表单验证
"member.form.validation.name_empty" = "姓名不能为空";
"member.form.validation.name_too_long" = "姓名长度不能超过20个字符";
// 已移除的验证 - 不再使用
// "member.form.validation.number_empty" = "编号不能为空";
// "member.form.validation.number_too_long" = "编号长度不能超过15个字符";
"member.form.validation.points_invalid" = "请输入有效的积分数值";
"member.form.validation.points_negative" = "初始积分不能为负数";
"member.form.validation.points_too_high" = "初始积分不能超过1000";
"member.form.validation.role_invalid" = "角色选择无效";
"member.form.validation.birth_date_future" = "出生日期不能是未来时间";
"member.form.validation.age_too_old" = "年龄不能超过150岁";
"member.form.validation.row_error" = "第%@行：%@";
// 已移除的验证 - 不再使用
// "member.form.validation.duplicate_number" = "编号 %@ 重复";

// MARK: - 成员角色
"member.role.son" = "儿子";
"member.role.daughter" = "女儿";
"member.role.father" = "爸爸";
"member.role.mother" = "妈妈";
"member.role.other" = "其他";

// MARK: - AI分析报告
"ai_analysis.title" = "AI分析报告";
"ai_analysis.report_title" = "学生行为分析报告";
"ai_analysis.parent_feedback_title" = "家长反馈";
"ai_analysis.analysis_content" = "分析内容";
"ai_analysis.professional_analysis" = "专业分析内容";
"ai_analysis.feedback_content" = "沟通反馈内容";
"ai_analysis.copy_report" = "复制报告";
"ai_analysis.copy_professional" = "复制分析报告";
"ai_analysis.copy_feedback" = "复制家长反馈";
"ai_analysis.save_tip" = "分析报告不会被保存，点击返回后将消失，建议自行复制保存";
"ai_analysis.student_info" = "学生信息";
"ai_analysis.generated_time" = "生成时间";
"ai_analysis.total_records" = "总记录";
"ai_analysis.positive_records" = "加分记录";
"ai_analysis.negative_records" = "扣分记录";
"ai_analysis.positive_ratio" = "积极行为比例";
"ai_analysis.record_count" = "行为记录数";
"ai_analysis.current_points" = "当前积分";
"ai_analysis.unknown_student" = "未知学生";
"ai_analysis.generate_button" = "生成分析报告";
"ai_analysis.no_network" = "网络连接不可用，请检查网络设置后重试";
"ai_analysis.generating" = "AI正在生成报告...";
"ai_analysis.please_wait" = "这可能需要几秒钟时间，请耐心等待";
"ai_analysis.copy_success" = "报告已复制到剪贴板";
"ai_analysis.permission_title" = "需要高级会员";
"ai_analysis.upgrade_button" = "立即升级";
"ai_analysis.select_report_type" = "选择报告类型";
"ai_analysis.report_type.professional" = "学生分析报告";
"ai_analysis.report_type.parent_feedback" = "家长反馈";
"ai_analysis.professional_description" = "专业分析报告";
"ai_analysis.feedback_description" = "与家长沟通";

// 错误提示
"ai_analysis.error.no_user" = "用户信息不存在，请重新登录";
"ai_analysis.error.no_student" = "学生信息不存在";
"ai_analysis.error.not_premium" = "AI分析功能仅对高级会员开放";
"ai_analysis.error.insufficient_records" = "积分记录不足，需要至少10条记录（当前%d条）";
"ai_analysis.error.no_network" = "网络连接不可用，请检查网络设置";
"ai_analysis.error.network" = "网络连接异常，请检查网络后重试";
"ai_analysis.error.permission_unknown" = "权限验证失败，请联系客服";
"ai_analysis.error.no_report" = "报告不存在";
"ai_analysis.error.unknown" = "未知错误";
"ai_analysis.error.generic" = "生成报告失败";

// 状态描述
"ai_analysis.status.checking" = "正在验证权限...";
"ai_analysis.status.ready" = "可以生成AI分析报告";
"ai_analysis.status.unknown" = "未知状态";

// 恢复提示
"ai_analysis.recovery.upgrade" = "请升级到高级会员以使用此功能";
"ai_analysis.recovery.more_records" = "请添加更多的积分记录后再尝试";
"ai_analysis.recovery.check_network" = "请检查网络连接后重试";
"ai_analysis.recovery.try_later" = "请稍后再试";
"ai_analysis.recovery.try_again" = "请重新尝试";
"ai_analysis.recovery.contact_support" = "如果问题持续存在，请联系客服";

// MARK: - AI分析数据模型相关
"ai_analysis.data.unknown_reason" = "未知原因";
"ai_analysis.data.unknown_student" = "未知学生";
"ai_analysis.data.grade.kindergarten" = "幼儿园";
"ai_analysis.data.grade.primary_1" = "小学一年级";
"ai_analysis.data.grade.primary_2" = "小学二年级";
"ai_analysis.data.grade.primary_3" = "小学三年级";
"ai_analysis.data.grade.primary_4" = "小学四年级";
"ai_analysis.data.grade.primary_5" = "小学五年级";
"ai_analysis.data.grade.primary_6" = "小学六年级";
"ai_analysis.data.grade.primary" = "小学";

// MARK: - AI分析报告摘要格式
"ai_analysis.summary.basic_info" = "学生基本信息：%@";
"ai_analysis.summary.records_stat" = "行为记录统计：共%d条记录，其中加分%d次，扣分%d次";
"ai_analysis.summary.positive_ratio" = "积极行为比例：%@";
"ai_analysis.summary.generated_time" = "生成时间：%@";

// MARK: - 通用错误弹框
"common.error.title" = "错误";
"common.error.confirm" = "确定";

// MARK: - AI分析服务错误
"ai_analysis.service.unavailable" = "服务不可用";
"ai_analysis.service.request_in_progress" = "正在生成报告中，请稍候";
"ai_analysis.service.request_error" = "请求参数有误，请稍后重试";
"ai_analysis.service.auth_error" = "服务认证失败，请联系客服";
"ai_analysis.service.rate_limit" = "请求过于频繁，请稍后重试";

// MARK: - 添加成员错误信息
"add_member.error.no_family" = "当前没有选中的家庭";
"add_member.error.duplicate_numbers" = "以下编号已存在：%@";
"add_member.error.partial_failure" = "部分成员添加失败：成功%@个，失败%@个";

// MARK: - 班级重置功能
"class_reset.options.title" = "班级操作";
"class_reset.options.config" = "班级配置";
"class_reset.options.config_description" = "配置规则和奖品";
"class_reset.options.reset_points" = "重置学生积分";
"class_reset.options.reset_points_description" = "将所有学生积分重置为指定值";
"class_reset.options.reset_history" = "重置历史记录";
"class_reset.options.reset_history_description" = "清除所有学生的历史记录";

"class_reset.points.title" = "重置学生积分";
"class_reset.points.description" = "将 %@ 的所有学生积分重置为指定值";
"class_reset.points.student_count" = "将影响 %d 名学生";
"class_reset.points.input_label" = "新积分值";
"class_reset.points.input_placeholder" = "请输入0-1000的整数";
"class_reset.points.confirm_button" = "确认重置";
"class_reset.points.resetting" = "重置中...";

"class_reset.history.title" = "重置历史记录";
"class_reset.history.description" = "此操作将清除 %@ 所有学生的历史记录";
"class_reset.history.warning" = "⚠️ 此操作不可恢复";
"class_reset.history.details" = "将删除 %d 名学生共 %d 条历史记录";
"class_reset.history.confirm_button" = "确认清除";
"class_reset.history.resetting" = "清除中...";

"class_reset.validation.points_empty" = "请输入积分值";
"class_reset.validation.points_invalid" = "请输入有效的数字";
"class_reset.validation.points_negative" = "积分值不能为负数";
"class_reset.validation.points_too_high" = "积分值不能超过1000";

"class_reset.success.points_title" = "积分重置成功";
"class_reset.success.points_message" = "已将 %@ 的 %d 名学生积分重置为 %d 分";
"class_reset.success.history_title" = "历史记录清除成功";
"class_reset.success.history_message" = "已清除 %@ 的 %d 条历史记录";

"class_reset.error.points_failed" = "积分重置失败：%@";
"class_reset.error.history_failed" = "历史记录清除失败：%@";
"class_reset.error.no_students" = "班级中没有学生";

// MARK: - 创建家庭功能
"create_family.dialog.title" = "创建家庭";
"create_family.dialog.message" = "请输入家庭名称";
"create_family.dialog.placeholder" = "例如：我的家庭";
"create_family.dialog.create_button" = "创建";

"create_family.validation.name_empty" = "家庭名称不能为空";
"create_family.validation.name_too_long" = "家庭名称不能超过30个字符";
"create_family.validation.name_exists" = "家庭名称已存在，请更换名称";

"create_family.success.message" = "家庭创建成功";
"create_family.error.save_failed" = "家庭创建失败，请重试";

// MARK: - 创建班级功能
"create_class.permission_denied.title" = "权限不足";
"create_class.permission_denied.message" = "您当前为%@用户，最多可创建%d个班级。已创建%d个班级，无法继续创建。";
"create_class.permission_denied.upgrade_button" = "升级会员";
"create_class.permission_denied.free_user" = "免费";
"create_class.permission_denied.basic_user" = "初级会员";
"create_class.permission_denied.premium_user" = "高级会员";

"create_class.dialog.title" = "创建班级";
"create_class.dialog.message" = "请输入班级名称";
"create_class.dialog.placeholder" = "例如：一年级1班";
"create_class.dialog.create_button" = "创建";

// MARK: - 刮刮卡功能
"scratch_card.title" = "刮刮卡";
"scratch_card.remaining_format" = "剩余：%d 张";
"scratch_card.cost_format" = "每张：%d 积分";
"scratch_card.current_points_format" = "当前积分：%d";
"scratch_card.points_sufficient" = "积分充足";
"scratch_card.points_insufficient" = "积分不足";
"scratch_card.loading" = "加载刮刮卡配置中...";
"scratch_card.no_config" = "暂无刮刮卡配置";
"scratch_card.config_description" = "请前往设置页面配置刮刮卡奖品";
"scratch_card.go_settings" = "前往设置";
"scratch_card.back" = "返回";
"scratch_card.happy_title" = "开心刮刮卡";
"scratch_card.happy_subtitle" = "用手刮开涂层，发现惊喜奖品";
"scratch_card.hint_text" = "刮一刮";
"scratch_card.congratulations" = "恭喜中奖！";
"scratch_card.big_reveal" = "刮刮卡大奖揭晓";
"scratch_card.points_consumed_format" = "消耗积分：%d";
"scratch_card.confirm_claim" = "确认领取";
"scratch_card.insufficient_points_title" = "积分不足";
"scratch_card.insufficient_points_message_format" = "刮刮卡需要 %d 积分，当前积分：%d";

// MARK: - 刮刮卡状态
"scratch_card.status.available" = "可刮除";
"scratch_card.status.scratched" = "已刮开";
"scratch_card.status.selected" = "选中";
"scratch_card.status.scratching" = "刮除中";
"scratch_card.status.revealing" = "显示奖品";
"scratch_card.status.completed" = "已完成";
"scratch_card.status.prize_reveal" = "发现惊喜";
"scratch_card.status.won" = "已中奖";
"scratch_card.status.congratulations" = "恭喜获奖";

// MARK: - 抽奖功能
"lottery.menu.title" = "选择抽奖道具";
"lottery.menu.wheel" = "大转盘";
"lottery.menu.wheel_description" = "转动轮盘获得随机奖品";
"lottery.menu.blind_box" = "盲盒";
"lottery.menu.blind_box_description" = "开启盲盒发现惊喜";
"lottery.menu.scratch_card" = "刮刮卡";
"lottery.menu.scratch_card_description" = "刮开卡片揭晓奖品";

"lottery.permission.title" = "权限不足";
"lottery.permission.wheel_message" = "使用大转盘抽奖需要初级或高级会员权限";
"lottery.permission.blind_box_message" = "盲盒需要高级会员权限。立即升级解锁这些功能。";
"lottery.permission.scratch_card_message" = "刮刮卡需要高级会员权限。立即升级解锁这些功能。";
"lottery.permission.upgrade_button" = "立即升级";

// MARK: - 大转盘抽奖组件
"lottery_wheel.title" = "幸运大转盘";
"lottery_wheel.page_title" = "幸运大转盘";
"lottery_wheel.back_button" = "返回";
"lottery_wheel.current_points" = "%d分";
"lottery_wheel.no_config.title" = "未配置大转盘";
"lottery_wheel.no_config.description" = "当前班级尚未配置大转盘抽奖";
"lottery_wheel.go_settings" = "前往设置";
"lottery_wheel.mystery_prize" = "神秘奖品";
"lottery_wheel.empty_prize" = "空奖品";
"lottery_wheel.female_avatar" = "女生头像";
"lottery_wheel.male_avatar" = "男生头像";
"lottery_wheel.fireworks" = "烟花";
"lottery_wheel.cost_info" = "每次消耗：%d分";
"lottery_wheel.affordable" = "积分充足";
"lottery_wheel.insufficient_points" = "积分不足";
"lottery_wheel.confirm_result" = "领取奖品";
"lottery_wheel.insufficient_points_title" = "积分不足";
"lottery_wheel.insufficient_points_message" = "转盘抽奖需要%@分，但您当前只有%@分";
"lottery_wheel.empty_prizes.title" = "未设置奖品";
"lottery_wheel.empty_prizes.description" = "请在设置中配置转盘奖品";
"lottery_wheel.spin_button" = "开始抽奖";
"lottery_wheel.spinning" = "抽奖中...";

// MARK: - 全家操作功能
"family_operation.options.title" = "全家操作";
"family_operation.options.add_points" = "全家加分";
"family_operation.options.add_description" = "为当前家庭所有成员加分";
"family_operation.options.deduct_points" = "全家扣分";
"family_operation.options.deduct_description" = "为当前家庭所有成员扣分";

"family_operation.form.add_title" = "全家加分";
"family_operation.form.deduct_title" = "全家扣分";
"family_operation.form.name_label" = "操作名称";
"family_operation.form.name_placeholder" = "例如：家务表现优秀";
"family_operation.form.value_label" = "分值";
"family_operation.form.value_placeholder" = "请输入分值";
"family_operation.form.submit" = "确认操作";
"family_operation.form.submitting" = "操作中...";

"family_operation.type.add" = "加分";
"family_operation.type.deduct" = "扣分";

"family_operation.validation.name_empty" = "操作名称不能为空";
"family_operation.validation.name_too_long" = "操作名称长度不能超过20个字符";
"family_operation.validation.value_empty" = "分值不能为空";
"family_operation.validation.value_invalid" = "请输入有效的分值";
"family_operation.validation.value_positive" = "分值必须为正数";
"family_operation.validation.value_too_high" = "分值不能超过100";

"family_operation.error.no_family_selected" = "未选择家庭";

// MARK: - 班级配置功能
"Class Configuration" = "班级配置";
"add points rules" = "常用加分规则";
"deduct rules" = "常用扣分规则";
"prizes config" = "班级奖品配置";

"Common rules for adding points" = "加分规则";
"Common rules for deduting points" = "扣分规则";
"Class Prize Configuration" = "奖品配置";

"Import from the library" = "从库中导入";
"Save" = "保存配置";
"Saving..." = "保存中...";

"Configuration successful" = "配置成功";
"Class configuration has been successfully saved." = "班级配置已成功保存";

"class_config.error.validation_failed" = "数据验证失败";
"class_config.error.save_failed" = "保存失败，请重试";
"No class selected" = "未选择班级";
"There is valid configuration data." = "没有有效的配置数据";

// MARK: - 规则配置功能
"Rule configuration" = "规则配置";
"Add points rules" = "配置加分规则";
"Set the commonly reasons and points for adding points" = "设置常用的加分原因和分值";
"Dedect points rules" = "配置扣分规则";
"Set the commonly reasons and points for deducting points" = "设置常用的扣分原因和分值";

"Rules of adding points" = "配置加分规则";
"Rules of deducting points" = "配置扣分规则";
"rule %@" = "规则 %@";
"rule name" = "规则名称";
"e.g. Active in class" = "例如：课堂发言积极";
"e.g. Being late" = "例如：迟到";
"Points" = "分值";
"5" = "5";
"Add" = "确认添加";
"Addting" = "添加中...";

"Rule name cannot be empty" = "规则名称不能为空";
"Rule name cannot exceed 20 characters" = "规则名称长度不能超过20个字符";
"Rule name cannot exceed 20 characters" = "分值不能为空";
"Please enter a valid points value" = "请输入有效的分值";
"Points cost must be greater than 0" = "分值必须为正数";
"Points cost cannot exceed 1000" = "分值不能超过100";
"Rule name already exists" = "规则名称已存在";
"rule_config.validation.row_error" = "第%@行：%@";
"rule_config.validation.internal_duplicate" = "与第%@行重复";

"Added Successfully" = "创建成功";
"Rule successfully added to the prize library" = "规则已成功添加到规则库";
"Some rules failed to add: %@ succeeded, %@ failed" = "部分规则创建失败：成功%@个，失败%@个";

"Configured add points rules" = "已配置加分规则";
"Configured deduct points rules" = "已配置扣分规则";
"No configured add points rules yet\nGo create the first one" = "暂无已配置的加分规则\n快去创建第一个吧";
"No configured deduct points rules yet\nGo create the first one" = "暂无已配置的扣分规则\n快去创建第一个吧";
"create_class.dialog.cancel_button" = "取消";

// MARK: - 规则配置功能
"Select rule type" = "选择规则类型";
"rule_config.options.add_rules" = "配置加分规则";
"Set the commonly reasons and points for adding points" = "设置学生表现良好的加分规则";
"Dedect points rules" = "配置扣分规则";
"Set the commonly reasons and points for deducting points" = "设置学生表现不佳的扣分规则";

"Rules of adding points" = "配置加分规则";
"Rules of deducting points" = "配置扣分规则";
"rule %@" = "规则 %@";
"rule name" = "规则名称";
"Points" = "分值";
"e.g. Active in class" = "例如：课堂发言积极";
"e.g. Being late" = "例如：迟到";
"5" = "例如：5";
"Add" = "确认添加";
"Addting" = "添加中...";

"Rule name cannot be empty" = "规则名称不能为空";
"Rule name cannot exceed 20 characters" = "规则名称不能超过20个字符";
"Rule name cannot exceed 20 characters" = "分值不能为空";
"Please enter a valid points value" = "分值必须为正整数";
"Points cost must be greater than 0" = "分值必须大于0";
"Points cost cannot exceed 1000" = "分值不能超过100";
"Rule name already exists" = "规则名称已存在";
"rule_config.validation.row_error" = "第%@行：%@";
"rule_config.validation.internal_duplicate" = "与第%@行重复";

"Added Successfully" = "添加成功";
"Rule successfully added to the prize library" = "规则已成功添加到规则库";
"Some rules failed to add: %@ succeeded, %@ failed" = "部分规则添加失败：成功%@个，失败%@个";

// MARK: - 删除班级功能
"delete_class.button.title" = "删除";
"delete_class.confirmation.title" = "删除班级";
"delete_class.confirmation.message" = "确定要删除班级\"%@\"吗？\n删除班级将会同时删除班级内的所有学生数据，此操作不可撤销。";
"delete_class.confirmation.confirm_button" = "确认删除";
"delete_class.confirmation.cancel_button" = "取消";

"create_class.validation.name_empty" = "班级名称不能为空";
"create_class.validation.name_too_long" = "班级名称不能超过30个字符";
"create_class.validation.name_exists" = "班级名称已存在，请更换名称";

"create_class.success.message" = "班级创建成功";
"create_class.error.save_failed" = "班级创建失败，请重试";

// MARK: - 奖品配置功能
"prize_config.form.title" = "配置奖品库";
"prize_config.form.prize_number_label" = "奖品 %@";
"Prize name" = "奖品名称";
"prize_config.form.name_placeholder" = "例如：小红花";
"prize_config.form.prize_type" = "奖品类型";
"Cost" = "所需积分";
"prize_config.form.cost_placeholder" = "10";
"prize_config.form.submit" = "确认添加";
"prize_config.form.submitting" = "添加中...";
"prize_config.form.configured_prizes" = "已配置奖品";

"prize_config.validation.name_empty" = "奖品名称不能为空";
"prize_config.validation.name_too_long" = "奖品名称不能超过20个字符";
"prize_config.validation.cost_empty" = "积分成本不能为空";
"prize_config.validation.cost_invalid" = "请输入有效的积分数值";
"prize_config.validation.cost_positive" = "积分成本必须大于0";
"prize_config.validation.cost_too_high" = "积分成本不能超过1000";
"prize_config.validation.duplicate_name" = "奖品名称已存在";
"prize_config.validation.row_error" = "第%@行：%@";
"prize_config.validation.internal_duplicate" = "与第%@行重复";

"prize_config.success.title" = "添加成功";
"prize_config.success.message" = "奖品已成功添加到奖品库";
"prize_config.error.partial_failure" = "部分奖品添加失败：成功%@个，失败%@个";

"prize_config.configured_list.title" = "已配置奖品";
"prize_config.configured_list.empty" = "暂无已配置的奖品\n快去创建第一个吧";

"prize_config.delete.confirmation.title" = "删除奖品";
"prize_config.delete.confirmation.message" = "确定要删除奖品\"%@\"吗？\n此操作不可撤销。";
"prize_config.delete.confirmation.confirm" = "确认删除";
"prize_config.delete.confirmation.cancel" = "取消";

// MARK: - 抽奖道具配置功能
"lottery_tool_config.title" = "抽奖道具配置";
"lottery_tool_config.select_class.title" = "选择班级";
"lottery_tool_config.select_class.description" = "请选择要配置抽奖道具的班级";
"lottery_tool_config.select_tool_type.title" = "选择抽奖道具";
"lottery_tool_config.select_tool_type.description" = "请选择要配置的抽奖道具类型";

"lottery_tool_config.tool_type.wheel" = "大转盘";
"lottery_tool_config.tool_type.box" = "盲盒";
"lottery_tool_config.tool_type.scratch" = "刮刮卡";

"lottery_tool_config.tool_description.wheel" = "经典转盘抽奖，4-12固定分区，刺激有趣";
"lottery_tool_config.tool_description.box" = "神秘盲盒抽奖，1-20个可选，充满惊喜";
"lottery_tool_config.tool_description.scratch" = "刮刮卡抽奖，1-20个可选，手感真实";

"lottery_tool_config.status.configured" = "已配置";
"lottery_tool_config.status.not_configured" = "未配置";
"lottery_tool_config.action.configure" = "点击开始配置";
"lottery_tool_config.action.modify" = "点击修改配置";

"lottery_tool_config.form.basic_config" = "基本配置";
"lottery_tool_config.form.item_config" = "项目配置";
"lottery_tool_config.form.section_config" = "分区配置";
"lottery_tool_config.form.item_count" = "道具数量";
"lottery_tool_config.form.section_count" = "分区数量";
"lottery_tool_config.form.cost_per_play" = "每次抽奖消耗积分";
"lottery_tool_config.form.prize_name" = "奖品名称";
"lottery_tool_config.form.fixed_sections" = "大转盘支持4-12个可选分区";
"lottery_tool_config.form.suggested_range" = "建议范围：1-50 积分";
"lottery_tool_config.form.range_format" = "范围：%d - %d";

"lottery_tool_config.item.section_prefix" = "分区";
"lottery_tool_config.item.box_prefix" = "盲盒";
"lottery_tool_config.item.scratch_prefix" = "刮刮卡";
"lottery_tool_config.item.section_title" = "分区 %d";
"lottery_tool_config.item.box_title" = "盲盒 %d";
"lottery_tool_config.item.scratch_title" = "刮刮卡 %d";
"lottery_tool_config.item.section_placeholder" = "请输入分区 %d 的奖品名称";
"lottery_tool_config.item.box_placeholder" = "请输入盲盒 %d 的奖品名称";
"lottery_tool_config.item.scratch_placeholder" = "请输入刮刮卡 %d 的奖品名称";

"lottery_tool_config.validation.error_title" = "配置错误";
"lottery_tool_config.validation.item_count_invalid" = "道具数量必须在 %d 到 %d 之间";
"lottery_tool_config.validation.cost_invalid" = "每次抽奖消耗积分必须大于0";
"lottery_tool_config.validation.cost_too_high" = "每次抽奖消耗积分不能超过1000";
"lottery_tool_config.validation.prize_name_empty" = "还有 %d 个%@的奖品名称未填写";
"lottery_tool_config.validation.prize_name_too_long" = "第%d个%@的奖品名称不能超过20个字符";
"lottery_tool_config.validation.duplicate_prize_names" = "存在重复的奖品名称，请检查并修改";

"lottery_tool_config.result.save_success" = "配置已保存！";
"lottery_tool_config.result.update_success" = "配置已更新！";
"lottery_tool_config.result.save_success_title" = "配置成功";
"lottery_tool_config.result.update_success_title" = "更新成功";
"lottery_tool_config.result.save_failed" = "保存失败：%@";

"lottery_tool_config.empty_state.no_classes" = "暂无班级";
"lottery_tool_config.empty_state.create_class_first" = "请先创建班级，然后再配置抽奖道具";
"lottery_tool_config.empty_state.no_matching_classes" = "未找到匹配的班级";
"lottery_tool_config.empty_state.try_other_keywords" = "请尝试其他搜索关键词";

"lottery_tool_config.class_info.current_class" = "当前班级";
"lottery_tool_config.class_info.student_count" = "学生数量";
"lottery_tool_config.class_info.student_count_format" = "%d 人";
"lottery_tool_config.class_info.configured_tools" = "已配置 %d 种抽奖道具";
"lottery_tool_config.class_info.total_points" = "%d 总积分";

"lottery_tool_config.button.save" = "保存";
"lottery_tool_config.button.complete" = "完成";
"lottery_tool_config.button.cancel" = "取消";
"lottery_tool_config.button.back" = "返回";
"lottery_tool_config.button.clear" = "清除";
"lottery_tool_config.button.search_placeholder" = "搜索班级名称";

// MARK: - 历史记录删除功能
"history.delete.confirm_title" = "确认删除记录";
"history.delete.confirm_message" = "此操作无法撤销，请确认删除";
"history.delete.impact_title" = "删除影响";
"history.delete.cancel" = "取消";
"history.delete.confirm" = "确认删除";
"history.delete.success" = "记录删除成功";
"history.delete.failed" = "记录删除失败";
"history.delete.insufficient_points" = "删除失败：回滚后积分将为负数";

// MARK: - 记录类型本地化
"record.type.points" = "积分";
"record.type.redemption" = "兑换";
"record.type.wheel_lottery" = "大转盘";
"record.type.box_lottery" = "盲盒";
"record.type.scratch_lottery" = "刮刮卡";

// MARK: - 删除影响描述模板
"history.delete.impact.add_points" = "删除后将扣除 %d 积分";
"history.delete.impact.deduct_points" = "删除后将返还 %d 积分";
"history.delete.impact.return_points" = "删除后将返还 %d 积分";
"history.delete.impact.no_change" = "删除此记录不会影响积分";

// MARK: - 记录项默认名称
"history.record.default_point" = "积分记录";
"history.record.default_redemption" = "兑换记录";
"history.record.default_lottery" = "抽奖记录";

// MARK: - 学生积分操作功能
"student_points.options.add_title" = "学生加分";
"student_points.options.deduct_title" = "学生扣分";
"student_points.options.frequent_rules_title" = "常用规则";
"student_points.options.no_frequent_rules" = "暂无常用规则\n可在设置页面配置规则库";
"student_points.options.no_member_rules" = "暂无预设规则\n点击下方按钮添加规则";
"student_points.options.custom_option" = "自定义";

// MARK: - 家庭成员规则管理
"member_rule.config.add_title" = "添加加分规则";
"member_rule.config.deduct_title" = "添加扣分规则";
"member_rule.config.subtitle" = "为 %@ 配置专属规则";
"member_rule.config.rule_name" = "规则名称";
"member_rule.config.rule_value" = "分值";
"member_rule.config.add_placeholder" = "例如：完成作业";
"member_rule.config.deduct_placeholder" = "例如：迟到";
"member_rule.config.value_placeholder" = "5";
"member_rule.config.confirm_add" = "确认添加";
"member_rule.config.submitting" = "提交中...";
"member_rule.config.cancel" = "取消";
"member_rule.config.add_rule" = "添加规则";

// MARK: - 规则操作提示
"member_rule.delete.title" = "删除规则";
"member_rule.delete.message" = "确定要删除规则「%@」吗？此操作无法撤销。";
"member_rule.delete.confirm" = "删除";
"member_rule.delete.cancel" = "取消";

// MARK: - 错误提示
"member_rule.error.network_title" = "网络错误";
"member_rule.error.network_message" = "网络连接异常，请检查网络设置后重试";
"member_rule.error.creation_failed" = "规则创建失败";
"member_rule.error.deletion_failed" = "删除失败";
"member_rule.error.operation_failed" = "操作失败";
"member_rule.error.retry" = "重试";
"member_rule.error.confirm" = "确定";

"student_points.form.title" = "自定义积分操作";
"student_points.form.items_title" = "操作项目";
"student_points.form.add_item" = "添加项";
"student_points.form.name_label" = "操作名称";
"student_points.form.name_placeholder" = "例如：课堂表现优秀";
"student_points.form.value_label" = "分值";
"student_points.form.value_placeholder" = "例如：5";
"student_points.form.preview_title" = "操作预览";
"student_points.form.cancel" = "取消";
"student_points.form.confirm" = "确认";
"student_points.form.validation_errors" = "输入错误";

"student_points.validation.name_empty" = "操作名称不能为空";
"student_points.validation.value_empty" = "分值不能为空";
"student_points.validation.value_invalid" = "请输入有效的分值";
"student_points.validation.value_positive" = "分值必须为正数";

// MARK: - 兑换功能
"redemption.operation.success" = "兑换成功";
"redemption.operation.failed" = "兑换失败";
"redemption.operation.insufficient" = "积分不足";
"redemption.operation.invalid" = "无效兑换";

// MARK: - 兑换选项弹窗
"redemption.options.title" = "兑换奖品";
"redemption.options.current_points" = "当前积分：%@分";
"redemption.options.prizes_title" = "可兑换奖品";
"redemption.options.points_unit" = "分";
"redemption.options.insufficient" = "积分不足";
"redemption.options.no_prizes" = "暂无可兑换奖品\n可在设置页面配置";
"redemption.options.or" = "或";
"redemption.options.custom_title" = "自定义兑换";
"redemption.options.custom_button_title" = "自定义兑换";
"redemption.options.custom_button_description" = "兑换其他物品";
"redemption.options.custom_subtitle" = "填写自定义兑换物品";
"redemption.options.success_message" = "兑换成功！";

// MARK: - 兑换表单
"redemption.form.title" = "自定义兑换";
"redemption.form.info.title" = "兑换说明";
"redemption.form.info.subtitle" = "当前积分：%@分";
"redemption.form.items.title" = "兑换项目";
"redemption.form.item.title" = "项目 %@";
"redemption.form.item.name" = "物品名称";
"redemption.form.item.name.placeholder" = "请输入物品名称";
"redemption.form.item.cost" = "消耗积分";
"redemption.form.item.cost.placeholder" = "请输入消耗积分";
"redemption.form.add_item" = "添加项目";
"redemption.form.summary.title" = "兑换总计";
"redemption.form.summary.total" = "总消耗";
"redemption.form.summary.points" = "分";
"redemption.form.confirm" = "确认兑换";

// MARK: - 兑换表单验证
"redemption.form.error.no_valid_items" = "没有有效的兑换项目";
"redemption.form.error.empty_name" = "第%@项的名称不能为空";
"redemption.form.error.invalid_cost" = "第%@项的消耗积分无效";
"redemption.form.error.insufficient_points" = "积分不足，需要%@分，当前%@分";

// MARK: - 盲盒开箱功能
"blind_box.title" = "盲盒开箱";
"blind_box.no_config" = "暂无盲盒配置";
"blind_box.loading" = "加载盲盒配置中...";
"blind_box.empty_state.title" = "暂无盲盒配置";
"blind_box.empty_state.description" = "请先在设置中配置盲盒奖品";
"blind_box.empty_state.config_button" = "前往配置";

"blind_box.stats.total" = "总数";
"blind_box.stats.unopened" = "未开启";
"blind_box.stats.opened" = "已开启";
"blind_box.stats.cost_per_open" = "每次消耗";

"blind_box.item.title_format" = "盲盒 %@";
"blind_box.item.status.waiting" = "待开启";
"blind_box.item.status.opening" = "开启中";
"blind_box.item.status.completed" = "已完成";
"blind_box.item.status.opened" = "已开启";

"blind_box.result.congratulations" = "🎉 恭喜获得 🎉";
"blind_box.result.cost_info" = "消耗积分";
"blind_box.result.points_format" = "%@ 积分";
"blind_box.result.obtained" = "已获得";
"blind_box.result.cancel" = "取消";
"blind_box.result.confirm" = "确认领取";

"blind_box.alert.insufficient_points.title" = "积分不足";
"blind_box.alert.insufficient_points.message" = "开启盲盒需要 %@ 积分，当前积分：%@";
"blind_box.alert.no_config.title" = "未配置盲盒";
"blind_box.alert.no_config.message" = "请先在设置中配置盲盒奖品";
"blind_box.alert.go_settings" = "前往设置";
"blind_box.alert.confirm" = "确定";
"blind_box.alert.cancel" = "取消";

"blind_box.settings.title" = "盲盒配置";
"blind_box.settings.developing" = "此功能正在开发中，敬请期待...";

"blind_box.operation.success" = "盲盒开启成功";
"blind_box.operation.failed" = "盲盒开启失败";
"blind_box.operation.record_created" = "盲盒抽奖记录创建成功";
"blind_box.operation.record_failed" = "盲盒抽奖记录创建失败";

// MARK: - 配置信息格式化
"lottery_tool_config.config_info.item_count_format" = "%d 个项目";
"lottery_tool_config.config_info.cost_per_play_format" = "%d 积分/次";

// MARK: - 底部导航栏
"tab.home" = "首页";
"tab.settings" = "成长日记";
"tab.profile" = "我的";
"tab.current_selection_format" = "当前选中: %d";

// MARK: - 学生网格和管理
"student_grid.no_class.title" = "尚未创建班级";
"student_grid.no_class.description" = "请先创建一个班级，然后再添加学生";
"student_grid.no_class.create_button" = "创建班级";
"student_grid.unknown_name" = "未知";
"student_grid.unnamed_class" = "未命名班级";



// MARK: - 刮刮卡界面
"scratch_card.title" = "刮刮卡";
"scratch_card.remaining_format" = "剩余：%d 张";
"scratch_card.cost_format" = "每张：%d 积分";
"scratch_card.current_points_format" = "当前积分：%d";
"scratch_card.points_sufficient" = "积分充足";
"scratch_card.points_insufficient" = "积分不足";
"scratch_card.loading" = "加载刮刮卡配置中...";
"scratch_card.no_config" = "暂无刮刮卡配置";
"scratch_card.config_description" = "请前往设置页面配置刮刮卡奖品";
"scratch_card.go_settings" = "前往设置";
"scratch_card.back" = "返回";
"scratch_card.happy_title" = "开心刮刮卡";
"scratch_card.happy_subtitle" = "用手刮开涂层，发现惊喜奖品";
"scratch_card.hint_text" = "刮一刮";
"scratch_card.congratulations" = "恭喜中奖！";
"scratch_card.big_reveal" = "刮刮卡大奖揭晓";
"scratch_card.points_consumed_format" = "消耗积分：%d";
"scratch_card.confirm_claim" = "确认领取";
"scratch_card.insufficient_points_title" = "积分不足";
"scratch_card.insufficient_points_message_format" = "刮刮卡需要 %d 积分，当前积分：%d";

// MARK: - 刮刮卡状态
"scratch_card.status.available" = "可刮除";
"scratch_card.status.scratched" = "已刮开";
"scratch_card.status.selected" = "选中";
"scratch_card.status.scratching" = "刮除中";
"scratch_card.status.revealing" = "显示奖品";
"scratch_card.status.completed" = "已完成";
"scratch_card.status.prize_reveal" = "发现惊喜";
"scratch_card.status.won" = "已中奖";
"scratch_card.status.congratulations" = "恭喜获奖";

// MARK: - 盲盒界面
"blind_box.title" = "盲盒开箱";
"blind_box.no_config" = "暂无盲盒配置";
"blind_box.loading" = "加载盲盒配置中...";
"blind_box.config_description" = "请先在设置中配置盲盒奖品";
"blind_box.go_config" = "前往配置";

// MARK: - 试用功能
"trial.banner.available_text" = "你有一个限时福利可领取";
"trial.button.claim" = "立即领取";
"trial.status.available" = "30天高级会员试用等你领取";
"trial.status.active" = "试用剩余 %d 天";
"trial.status.expired" = "试用已过期";
"trial.error.already_claimed" = "您已经领取过试用";
"trial.error.claim_failed" = "试用领取失败，请稍后重试";

// MARK: - 试用弹窗
"trial.modal.title" = "限时福利";
"trial.modal.message" = "亲爱的家长，感谢您对孩子教育的关注，我们将赠送您一个月的高级会员，祝您家庭幸福！";
"trial.modal.benefits_title" = "试用期间您将享受：";
"trial.modal.benefit_1" = "创建最多5个班级";
"trial.modal.benefit_2" = "解锁所有抽奖道具";
"trial.modal.benefit_3" = "AI智能分析报告";
"trial.modal.benefit_4" = "配置更多的班级常用规则";
"trial.modal.claim_button" = "立即领取";

// MARK: - 订阅会员
"subscription.membership.premium_trial" = "高级会员（试用中）";
"blind_box.stats.total" = "总数";
"blind_box.stats.unopened" = "未开启";
"blind_box.stats.opened" = "已开启";
"blind_box.stats.cost_per_open" = "每次消耗";
"blind_box.item_format" = "盲盒 %d";
"blind_box.status.waiting" = "待开启";
"blind_box.status.opening" = "开启中";
"blind_box.status.completed" = "已完成";
"blind_box.status.opened" = "已开启";
"blind_box.congratulations" = "🎉 恭喜获得 🎉";
"blind_box.cost_info" = "消耗积分";
"blind_box.points_format" = "%d 积分";
"blind_box.obtained" = "已获得";
"blind_box.insufficient_points_title" = "积分不足";
"blind_box.insufficient_points_message_format" = "开启盲盒需要 %d 积分，当前积分：%d";
"blind_box.no_config_title" = "未配置盲盒";
"blind_box.no_config_message" = "请先在设置中配置盲盒奖品";

// MARK: - 大转盘界面
"lottery_wheel.go_settings" = "前往设置";
"lottery_wheel.mystery_prize" = "神秘奖品";
"lottery_wheel.empty_prize" = "空奖品";
"lottery_wheel.female_avatar" = "女生头像";
"lottery_wheel.male_avatar" = "男生头像";
"lottery_wheel.fireworks" = "烟花";

// MARK: - 班级配置
"class_config.unnamed_class" = "未命名班级";
"class_config.configured_tools_format" = "已配置 %d 种抽奖道具";
"class_config.student_count_with_unit" = "%d 人";
"class_config.unknown_class" = "未知班级";

// MARK: - 规则和奖品配置
"rule_config.type.add_points" = "加分";
"rule_config.type.deduct_points" = "扣分";
"prize_config.type.virtual" = "虚拟";

// MARK: - 学生积分操作补充
"student_points.options.custom_description_add" = "自定义加分原因和分值";
"student_points.options.custom_description_deduct" = "自定义扣分原因和分值";
"student_points.form.description_add" = "自定义加分原因和分值";
"student_points.form.description_deduct" = "自定义扣分原因和分值";
"student_points.form.submitting" = "提交中...";

// MARK: - 盲盒补充
"blind_box.page_title" = "盲盒开箱";
"blind_box.loading_config" = "加载盲盒配置中...";
"blind_box.back_button" = "返回";
"blind_box.result.congratulations_text" = "🎉 恭喜获得 🎉";
"blind_box.result.cost_label_text" = "消耗积分";
"blind_box.result.obtained_label_text" = "已获得";
"blind_box.result.confirm_button_text" = "确认领取";
"blind_box.alert.insufficient_points_title_text" = "积分不足";
"blind_box.alert.insufficient_points_message_text" = "开启盲盒需要 %@ 积分，当前积分：%@";
"blind_box.alert.confirm_button_text" = "确定";

// MARK: - 抽奖道具配置补充
"lottery_tool_config.wheel_title" = "大转盘配置";
"lottery_tool_config.box_title" = "盲盒配置";
"lottery_tool_config.scratch_title" = "刮刮卡配置";
"lottery_tool_config.section_count" = "分区数量";
"lottery_tool_config.box_count" = "盲盒数量";
"lottery_tool_config.scratch_count" = "刮刮卡数量";
"lottery_tool_config.section_config" = "分区配置";
"lottery_tool_config.box_config" = "盲盒配置";
"lottery_tool_config.scratch_config" = "刮刮卡配置";
"lottery_tool_config.section_number" = "分区 %d";
"lottery_tool_config.box_number" = "盲盒 %d";
"lottery_tool_config.scratch_number" = "刮刮卡 %d";

// MARK: - 订阅补充
"subscription.user_level_regular" = "普通用户";
"subscription.user_level_premium" = "高级用户";
"subscription.not_activated_message" = "未开通会员，无法解锁全部功能";
"subscription.membership.basic" = "初级会员";
"subscription.membership.premium" = "高级会员";
"subscription.feature.manage_classes_two" = "可管理两个班级";
"subscription.feature.unlock_wheel" = "解锁大转盘道具";
"subscription.feature.multi_device_sync" = "可配置更多班级常用规则";
"subscription.feature.basic_functions" = "学生积分管理、奖品兑换等基础功能";
"subscription.feature.all_basic_benefits" = "初级会员的所有权益";
"subscription.feature.manage_classes_five" = "可管理5个班级";
"subscription.feature.unlock_box_scratch" = "解锁抽奖道具盲盒和刮刮卡";
"subscription.feature.ai_reports" = "解锁AI生成分析报告的功能";
"subscription.pricing.monthly" = "月会员";
"subscription.pricing.yearly" = "年会员";
"subscription.subscribe_button" = "立即订阅";
"subscription.agreement_notice" = "订阅会员前请阅读《会员服务协议》";
"subscription.agreement_prompt" = "请在订阅前阅读";
"subscription.agreement_link" = "《会员服务协议》";
"subscription.user_info.not_member" = "未开通会员，无法解锁全部功能";
"subscription.not_activated" = "未开通";
"prize_config.type.physical" = "实物";
"prize_config.cost_format" = "%d积分 - %@";
"prize_config.form.type_label" = "奖品类型";

// MARK: - 加载和进度状态
"common.loading" = "加载中...";
"common.loading_config" = "加载配置中...";
"common.return" = "返回";
"common.go_back" = "返回";

// MARK: - 订阅界面
"subscription.not_member" = "未开通会员，无法解锁全部功能";
"subscription.crown_icon" = "皇冠(订阅）";
"subscription.premium_user" = "高级会员";
"subscription.regular_user" = "普通用户";
"subscription.not_activated" = "未开通";

// MARK: - 用户界面
"user_info.parent_nickname" = "家长";
"user_info.premium_member" = "高级会员";

// MARK: - 表单数据模型
"form_data.item_range_format" = "范围：%d - %d";
"form_data.section_prefix" = "分区";
"form_data.box_prefix" = "盲盒";
"form_data.scratch_prefix" = "刮刮卡";
"form_data.section_title_format" = "分区 %d";
"form_data.box_title_format" = "盲盒 %d";
"form_data.scratch_title_format" = "刮刮卡 %d";
"form_data.section_placeholder_format" = "请输入分区 %d 的奖品名称";
"form_data.box_placeholder_format" = "请输入盲盒 %d 的奖品名称";
"form_data.scratch_placeholder_format" = "请输入刮刮卡 %d 的奖品名称";

// MARK: - 验证消息
"validation.item_count_range" = "道具数量必须在 %d 到 %d 之间";
"validation.cost_positive" = "每次抽奖消耗积分必须大于0";
"validation.cost_too_high" = "每次抽奖消耗积分不能超过1000";
"validation.prize_name_length" = "第%d个%@的奖品名称不能超过20个字符";
"validation.empty_prize_names" = "还有 %d 个%@的奖品名称未填写";
"validation.duplicate_prize_names" = "存在重复的奖品名称，请检查并修改";

// MARK: - 预览数据
"preview_data.preview_user" = "预览用户";
"preview_data.grade_class" = "三年级一班";
"preview_data.student_format" = "学生%d";
"preview_data.male" = "男";
"preview_data.female" = "女";
"preview_data.big_wheel" = "大转盘";
"preview_data.zhang_xiaoming" = "张小明";
"preview_data.grade_three_class_one" = "三年级1班";
"preview_data.mysterious_prize" = "神秘奖品";
"preview_data.little_red_flower" = "小红花";
"preview_data.prize_format" = "奖品%d";

// MARK: - 学生详情页操作按钮
"student_detail.action.add_points" = "加分";
"student_detail.action.deduct_points" = "扣分";
"student_detail.action.exchange" = "兑换";
"student_detail.action.lottery" = "抽奖";
"student_detail.action.analysis" = "分析";

// MARK: - 性别
"gender.male" = "男";
"gender.female" = "女";
"gender.avatar.male" = "男生头像";
"gender.avatar.female" = "女生头像";

// MARK: - 学生信息
"student.info.unknown_name" = "未知";
"student.info.class_grade" = "班级信息";
"student.info.default_number" = "000";
"student.info.role_age_format" = "%@ · %d岁";

// MARK: - 规则配置表单
"rule_config.form.configured_rules" = "已配置规则";

// MARK: - 规则配置列表
"rule_config.configured_list.points_unit" = "分";
"rule_config.configured_list.add_label" = "加分";
"rule_config.configured_list.deduct_label" = "扣分";

// MARK: - 规则数量限制
"rule_config.max_count_format" = "最多%@条";
"rule_config.limit.free_user" = "免费用户最多设置5条规则";
"rule_config.limit.paid_user" = "付费用户最多设置10条规则";

// MARK: - 抽奖道具配置表单补充
"lottery_tool_config.tool_type.wheel" = "分区";
"lottery_tool_config.tool_type.box" = "盲盒";
"lottery_tool_config.tool_type.scratch" = "刮刮卡";
"lottery_tool_config.form.item_count_title" = "%@数量";
"lottery_tool_config.form.item_count_range" = "范围：%@ - %@";
"lottery_tool_config.form.item_config_title" = "%@配置";
"lottery_tool_config.item.display_title_wheel" = "分区 %@";
"lottery_tool_config.item.display_title_box" = "盲盒 %@";
"lottery_tool_config.item.display_title_scratch" = "刮刮卡 %@";
"lottery_tool_config.item.placeholder_wheel" = "请输入分区 %@ 的奖品名称";
"lottery_tool_config.item.placeholder_box" = "请输入盲盒 %@ 的奖品名称";
"lottery_tool_config.item.placeholder_scratch" = "请输入刮刮卡 %@ 的奖品名称";
"lottery_tool_config.validation.prize_name_too_long" = "第%@个%@的奖品名称不能超过20个字符";
"lottery_tool_config.validation.empty_prize_names" = "还有 %@ 个%@的奖品名称未填写";
"lottery_tool_config.validation.duplicate_prize_names" = "存在重复的奖品名称，请检查并修改";

// MARK: - 订阅与权限
"subscription.upgrade" = "立即升级";

// MARK: - 抽奖权限提示
"lottery.permission.wheel_title" = "需要初级会员";
"lottery.permission.wheel_message" = "使用大转盘抽奖需要初级或高级会员权限";
"lottery.permission.advanced_title" = "需要高级会员";
"lottery.permission.advanced_message" = "使用盲盒和刮刮卡功能需要高级会员权限";

// MARK: - 班级冻结/解冻弹窗
"class_freeze.title" = "会员降级提示";
"class_freeze.subtitle" = "您的会员已从%@降级为%@";
"class_freeze.instruction" = "请选择%d个要保持活跃的班级";
"class_freeze.description" = "其他班级将被冻结，升级会员后可解冻";
"class_freeze.confirm_button" = "确认";
"class_freeze.subscribe_button" = "订阅会员解锁更多班级";
"class_freeze.status.active" = "活跃";
"class_freeze.status.will_freeze" = "将冻结";
"class_freeze.student_count_format" = "%d名学生 · 创建于%@";

"class_unfreeze.title" = "会员升级提示";
"class_unfreeze.subtitle" = "您已成功升级为%@";
"class_unfreeze.instruction" = "您可以解冻%d个班级";
"class_unfreeze.description" = "请选择要解冻的班级，解冻后可立即使用";
"class_unfreeze.confirm_button" = "确认解冻";
"class_unfreeze.status.frozen" = "已冻结";
"class_unfreeze.status.will_unfreeze" = "将解冻";

// 删除账号功能
"account_deletion.title" = "删除账号";
"account_deletion.subtitle" = "永久删除您的账号和所有数据";
"account_deletion.warning.title" = "⚠️ 重要提示";
"account_deletion.warning.message" = "删除账号后，您的所有数据将永久丢失且无法恢复，包括：\n\n• 所有班级和学生信息\n• 积分记录和兑换记录\n• 抽奖记录和历史数据\n• 订阅信息\n\n此操作不可逆转，请谨慎操作。";
"account_deletion.first_confirmation.title" = "确认删除账号";
"account_deletion.first_confirmation.message" = "您确定要删除账号吗？此操作无法撤销。";
"account_deletion.second_confirmation.title" = "最终确认";
"account_deletion.second_confirmation.message" = "最后确认：请输入 \"删除\" 以确认删除操作";
"account_deletion.second_confirmation.placeholder" = "请输入：删除";
"account_deletion.second_confirmation.confirm_text" = "删除";
"account_deletion.confirm_button" = "确认删除";
"account_deletion.cancel_button" = "取消";
"account_deletion.delete_input_button" = "删除";
"account_deletion.deleting_progress" = "正在删除账号...";
"account_deletion.completed" = "账号删除完成";

// 删除步骤
"account_deletion.step.preparing" = "准备删除数据...";
"account_deletion.step.marking" = "标记多设备删除...";
"account_deletion.step.local_data" = "删除本地数据...";
"account_deletion.step.cloud_data" = "删除云端数据...";
"account_deletion.step.revoke_login" = "撤销登录凭证...";
"account_deletion.step.final_cleanup" = "最终清理...";

// 删除错误
"account_deletion.error.user_not_found" = "用户信息不存在";
"account_deletion.error.cloudkit_unavailable" = "CloudKit服务不可用";
"account_deletion.error.backup_failed" = "数据备份失败";
"account_deletion.error.local_deletion_failed" = "本地数据删除失败";
"account_deletion.error.cloud_deletion_failed" = "云端数据删除失败";
"account_deletion.error.revocation_failed" = "撤销登录凭证失败";
"account_deletion.error.multi_device_conflict" = "多设备冲突";
"account_deletion.error.network_error" = "网络错误，请检查网络连接";
"account_deletion.error.unknown" = "未知错误";

// 数据统计
"account_deletion.data_stats.classes" = "班级";
"account_deletion.data_stats.students" = "学生";
"account_deletion.data_stats.records" = "记录";
"account_deletion.data_stats.total" = "共计";

// 多设备提示
"account_deletion.multi_device.title" = "多设备同步";
"account_deletion.multi_device.message" = "删除操作将同步到所有登录设备，请确保其他设备已备份重要数据。";

// MARK: - 班级重置 - 历史记录弹窗
"class_reset.history.record_types_title" = "将清除的记录类型：";
"class_reset.history.record_type.points" = "• 所有积分变动记录";
"class_reset.history.record_type.exchange" = "• 所有奖品兑换记录";
"class_reset.history.record_type.lottery" = "• 所有抽奖活动记录";
"class_reset.history.notice" = "注意：学生当前积分不会改变，仅清除历史记录";
"class_reset.history.final_confirm_title" = "最终确认";
"class_reset.history.final_cancel" = "我再想想";
"class_reset.history.final_confirm_message" = "您确定要清除 %@ 的所有 %d 条历史记录吗？\n\n此操作将永久删除所有学生的积分记录、兑换记录和抽奖记录，且无法恢复。";

// MARK: - 班级重置 - 积分弹窗
"class_reset.points.preview_title" = "预览效果：";
"class_reset.points.preview_description" = "每位学生积分将重置为：";
"class_reset.points.alert_title" = "确认重置积分";
"class_reset.points.alert_message" = "确定要将 %@ 的 %d 名学生积分重置为 %d 分吗？此操作不可撤销。";
"common.points_unit" = "分";

// MARK: - RevenueCat订阅系统
"subscription.level.free" = "免费版";
"subscription.level.basic" = "初级会员";
"subscription.level.premium" = "高级会员";

"subscription.feature.basic_scoring" = "基础积分管理";
"subscription.feature.prize_exchange" = "奖品兑换";
"subscription.feature.lottery" = "大转盘抽奖";
"subscription.feature.advanced_games" = "盲盒/刮刮卡";
"subscription.feature.ai_analysis" = "AI分析报告";
"subscription.feature.multi_device_sync" = "多设备同步";
"subscription.feature.create_multiple_classes" = "创建多个班级";

"subscription.status.not_subscribed" = "未订阅";
"subscription.valid_until" = "有效期至";
"subscription.trial_success_message" = "恭喜！您已成功领取30天高级会员试用！";

// MARK: - 权限管理
"permission.feature.available" = "功能可用";
"permission.feature.free" = "免费功能";
"permission.feature.requires_basic" = "需要初级会员";
"permission.feature.requires_premium" = "需要高级会员";

"permission.ai_analysis.requires_premium" = "AI分析报告需要高级会员";
"permission.ai_analysis.insufficient_records" = "积分记录不足，当前有{count}条记录，需要至少10条";

"permission.lottery.wheel_requires_basic" = "大转盘抽奖需要初级会员";
"permission.lottery.advanced_requires_premium" = "盲盒和刮刮卡需要高级会员";

"permission.upgrade_hint.free_to_basic" = "升级到初级会员，解锁更多功能";
"permission.upgrade_hint.basic_to_premium" = "升级到高级会员，享受完整功能";

// MARK: - 购买流程
"purchase.agreement.title" = "购买协议";
"purchase.agreement.message" = "请在购买前阅读《会员服务协议》";
"purchase.agreement.reminder" = "请先勾选同意《会员服务协议》";
"purchase.in_progress" = "购买中...";
"purchase.processing" = "正在处理...";
"purchase.success" = "订阅成功";
"purchase.success_processing" = "订阅成功！正在为您跳转...";
"purchase.failed" = "购买失败";
"purchase.restore.success" = "恢复购买成功！";
"purchase.restore.failed" = "恢复购买失败";
"subscription.success.message" = "恭喜您成功订阅会员服务！\n现在可以享受更多功能了";

// MARK: - 产品信息
"product.loading" = "加载中...";
"product.unavailable" = "产品不可用";
"product.monthly" = "月会员";
"product.yearly" = "年会员";

// MARK: - 试用功能
"trial.claim" = "领取试用";
"trial.duration" = "30天试用";
"trial.already_claimed" = "您已经享受过试用";
"trial.premium_member" = "您已经是付费会员";

// MARK: - 试用期订阅提醒
"trial.reminder.title" = "温馨提醒";
"trial.reminder.message" = "现在订阅会提前结束试用，建议试用期结束后再订阅";
"trial.reminder.confirm" = "谢谢提醒";

// MARK: - 错误信息
"error.revenuecat.configuration" = "订阅服务配置失败";
"error.revenuecat.purchase" = "购买失败，请重试";
"error.revenuecat.restore" = "恢复购买失败";
"error.revenuecat.network" = "网络连接失败，请检查网络";
"error.revenuecat.invalid_product" = "产品信息无效";

// MARK: - 班级数量限制
"class_limit.free.title" = "创建班级受限";
"class_limit.free.message" = "免费用户只能创建1个班级，升级到初级会员可创建2个班级";
"class_limit.basic.title" = "创建班级受限";
"class_limit.basic.message" = "初级会员最多可创建2个班级，升级到高级会员可创建5个班级";
"class_limit.premium.title" = "创建班级受限";
"class_limit.premium.message" = "高级会员最多可创建5个班级";
"class_limit.upgrade_button" = "升级会员";

// MARK: - 订阅降级处理
"subscription.downgrade.title" = "会员降级通知";
"subscription.downgrade.message" = "您的会员已降级，需要选择保留哪个班级继续管理";
"subscription.downgrade.select_class" = "选择要保留的班级";
"subscription.downgrade.freeze_others" = "其他班级将被冻结";

"subscription.upgrade.title" = "会员升级通知";
"subscription.upgrade.message" = "您的会员已升级，可以解冻之前被冻结的班级";
"subscription.upgrade.unfreeze_classes" = "解冻班级";

// MARK: - 功能锁定提示
"feature_lock.lottery.title" = "抽奖功能";
"feature_lock.lottery.message" = "大转盘抽奖需要初级会员及以上";
"feature_lock.advanced_games.title" = "高级游戏";
"feature_lock.advanced_games.message" = "盲盒和刮刮卡需要高级会员";
"feature_lock.ai_analysis.title" = "AI分析报告";
"feature_lock.ai_analysis.message" = "AI分析报告需要高级会员，且学生需要有至少10条积分记录";

// MARK: - 订阅错误信息（用户友好版本）
"subscription.error.configuration_failed" = "订阅服务初始化失败，请稍后重试";
"subscription.error.loading_failed" = "加载订阅信息失败，请检查网络连接";
"subscription.error.products_not_loaded" = "产品信息加载中，请稍后重试";
"subscription.error.product_not_found" = "所选产品暂时不可用，请稍后重试";
"subscription.error.purchase_failed" = "购买失败，请稍后重试";
"subscription.error.restore_failed" = "恢复购买失败，请稍后重试";
"subscription.error.user_not_found" = "用户信息异常，请重新登录";
"subscription.error.already_premium" = "您已经是会员用户";

// MARK: - CloudKit 错误信息
"cloudkit.sync.error" = "同步出现问题，请稍后重试";

// MARK: - 订阅按钮
"Subscription plan" = "查看订阅方案";