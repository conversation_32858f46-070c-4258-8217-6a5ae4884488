//
//  ContentView.swift
//  ztt2
//
//  Created by rainkygong on 2025/7/29.
//

import SwiftUI
import CoreData

struct ContentView: View {
    @Environment(\.managedObjectContext) private var viewContext

    var body: some View {
        VStack {
            Text("转团团")
                .font(.largeTitle)
                .padding()

            Text("应用正在启动...")
                .foregroundColor(.secondary)

            Button("测试 Core Data") {
                testCoreData()
            }
            .padding()
        }
        .onAppear {
            // 延迟加载主界面
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                // 这里可以切换到主界面
            }
        }
    }

    private func testCoreData() {
        print("测试 Core Data 连接...")

        // 创建一个简单的测试用户
        let user = User(context: viewContext)
        user.id = UUID()
        user.nickname = "测试用户"
        user.createdAt = Date()

        do {
            try viewContext.save()
            print("Core Data 测试成功！")
        } catch {
            print("Core Data 测试失败: \(error)")
        }
    }
}

#Preview {
    ContentView().environment(\.managedObjectContext, PersistenceController.preview.container.viewContext)
}
