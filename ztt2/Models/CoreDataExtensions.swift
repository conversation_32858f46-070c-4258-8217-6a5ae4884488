//
//  CoreDataExtensions.swift
//  ztt2
//
//  Created by Augment Agent on 2025/7/30.
//

import Foundation
import CoreData

// MARK: - User Extensions
extension User {
    
    /// 获取用户的所有成员
    var allMembers: [Member] {
        let set = members as? Set<Member> ?? []
        return Array(set).sorted { $0.memberNumber < $1.memberNumber }
    }
    
    /// 获取用户的所有全局规则
    var allGlobalRules: [GlobalRule] {
        let set = globalRules as? Set<GlobalRule> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }
    
    /// 获取常用的全局规则
    var frequentGlobalRules: [GlobalRule] {
        return allGlobalRules.filter { $0.isFrequent }
    }
    
    /// 计算全家总积分
    var totalFamilyPoints: Int32 {
        return allMembers.reduce(0) { $0 + $1.currentPoints }
    }
    
    /// 获取当前订阅状态
    var isSubscriptionActive: Bool {
        return subscription?.isActive ?? false
    }
    
    /// 获取订阅类型
    var subscriptionType: String {
        return subscription?.subscriptionType ?? "free"
    }
    
    /// 检查是否为高级会员
    var isPremiumMember: Bool {
        return subscriptionType == "premium" && isSubscriptionActive
    }
    
    /// 检查是否为初级会员或以上
    var isBasicMemberOrAbove: Bool {
        let type = subscriptionType
        return (type == "basic" || type == "premium") && isSubscriptionActive
    }
}

// MARK: - Member Extensions
extension Member {
    
    /// 计算年龄
    var age: Int {
        guard let birthDate = birthDate else { return 0 }
        let calendar = Calendar.current
        let ageComponents = calendar.dateComponents([.year], from: birthDate, to: Date())
        return ageComponents.year ?? 0
    }
    
    /// 获取所有积分记录（按时间倒序）
    var allPointRecords: [PointRecord] {
        let set = pointRecords as? Set<PointRecord> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }

    /// 获取行为相关的积分记录（排除兑换和抽奖）
    var behaviorPointRecords: [PointRecord] {
        return allPointRecords.filter {
            ($0.recordType ?? "behavior") == "behavior" && !($0.isReversed)
        }
    }

    /// 获取所有日记条目（按时间倒序）
    var allDiaryEntries: [DiaryEntry] {
        let set = diaryEntries as? Set<DiaryEntry> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }

    /// 获取所有AI报告（按创建时间倒序）
    var allAIReports: [AIReport] {
        let set = aiReports as? Set<AIReport> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 > createdAt2
        }
    }

    /// 获取成员规则
    var allMemberRules: [MemberRule] {
        let set = memberRules as? Set<MemberRule> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }

    /// 获取常用成员规则
    var frequentMemberRules: [MemberRule] {
        return allMemberRules.filter { $0.isFrequent }
    }

    /// 获取成员奖品
    var allMemberPrizes: [MemberPrize] {
        let set = memberPrizes as? Set<MemberPrize> ?? []
        return Array(set).sorted {
            let createdAt1 = $0.createdAt ?? Date.distantPast
            let createdAt2 = $1.createdAt ?? Date.distantPast
            return createdAt1 < createdAt2
        }
    }

    /// 获取兑换记录（按时间倒序）
    var allRedemptionRecords: [RedemptionRecord] {
        let set = redemptionRecords as? Set<RedemptionRecord> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }

    /// 获取抽奖记录（按时间倒序）
    var allLotteryRecords: [LotteryRecord] {
        let set = lotteryRecords as? Set<LotteryRecord> ?? []
        return Array(set).sorted {
            let timestamp1 = $0.timestamp ?? Date.distantPast
            let timestamp2 = $1.timestamp ?? Date.distantPast
            return timestamp1 > timestamp2
        }
    }
    
    /// 检查是否可以生成AI分析报告
    var canGenerateAnalysisReport: Bool {
        return behaviorPointRecords.count >= 10
    }
    
    /// 检查是否可以生成成长报告
    var canGenerateGrowthReport: Bool {
        return allDiaryEntries.count >= 10
    }
    
    /// 检查是否为孩子角色
    var isChild: Bool {
        let memberRole = role ?? ""
        return memberRole == "son" || memberRole == "daughter"
    }

    /// 获取显示名称
    var displayName: String {
        return name ?? "未知成员"
    }

    /// 获取角色显示名称
    var roleDisplayName: String {
        switch role ?? "" {
        case "father":
            return "爸爸"
        case "mother":
            return "妈妈"
        case "son":
            return "儿子"
        case "daughter":
            return "女儿"
        default:
            return "其他"
        }
    }

    /// 获取头像图片名称
    var avatarImageName: String {
        switch role ?? "" {
        case "father":
            return "person.fill"
        case "mother":
            return "person.fill"
        case "son":
            return "person.fill"
        case "daughter":
            return "person.fill"
        default:
            return "person.fill"
        }
    }

    /// 获取系统头像名称
    var systemAvatarName: String {
        switch role ?? "" {
        case "father":
            return "person.fill"
        case "mother":
            return "person.fill"
        case "son":
            return "person.fill"
        case "daughter":
            return "person.fill"
        default:
            return "person.circle.fill"
        }
    }
}

// MARK: - PointRecord Extensions
extension PointRecord {
    
    /// 获取记录类型显示名称
    var recordTypeDisplayName: String {
        switch recordType ?? "behavior" {
        case "behavior":
            return "行为记录"
        case "redemption":
            return "奖品兑换"
        case "lottery":
            return "抽奖消耗"
        default:
            return "未知类型"
        }
    }

    /// 获取积分变化描述
    var pointChangeDescription: String {
        let pointValue = value
        let sign = pointValue >= 0 ? "+" : ""
        return "\(sign)\(pointValue)"
    }
}

// MARK: - AIReport Extensions
extension AIReport {
    
    /// 获取报告类型显示名称
    var reportTypeDisplayName: String {
        switch reportType ?? "analysis" {
        case "analysis":
            return "行为分析报告"
        case "growth":
            return "成长分析报告"
        default:
            return "未知报告"
        }
    }

    /// 获取格式化的创建时间
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt ?? Date())
    }
}

// MARK: - Subscription Extensions
extension Subscription {
    
    /// 获取订阅类型显示名称
    var subscriptionTypeDisplayName: String {
        switch subscriptionType ?? "free" {
        case "free":
            return "免费版"
        case "basic":
            return "初级会员"
        case "premium":
            return "高级会员"
        default:
            return "未知类型"
        }
    }
    
    /// 检查订阅是否即将过期（7天内）
    var isExpiringSoon: Bool {
        guard let endDate = endDate else { return false }
        let sevenDaysFromNow = Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()
        return endDate <= sevenDaysFromNow
    }
    
    /// 获取剩余天数
    var remainingDays: Int {
        guard let endDate = endDate else { return 0 }
        let calendar = Calendar.current
        let components = calendar.dateComponents([.day], from: Date(), to: endDate)
        return max(0, components.day ?? 0)
    }
}

// MARK: - LotteryConfig Extensions
extension LotteryConfig {
    
    /// 获取抽奖道具类型显示名称
    var toolTypeDisplayName: String {
        switch toolType ?? "" {
        case "wheel":
            return "大转盘"
        case "blindbox":
            return "盲盒"
        case "scratchcard":
            return "刮刮卡"
        default:
            return "未知道具"
        }
    }
    
    /// 获取所有抽奖项目（按索引排序）
    var allItems: [LotteryItem] {
        let set = items as? Set<LotteryItem> ?? []
        return Array(set).sorted { $0.itemIndex < $1.itemIndex }
    }
}
