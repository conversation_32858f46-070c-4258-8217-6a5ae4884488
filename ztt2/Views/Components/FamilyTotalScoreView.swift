//
//  FamilyTotalScoreView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/30.
//

import SwiftUI

/**
 * 全家总积分弹窗组件
 * 用于显示全家总积分统计和时间范围选择
 */
struct FamilyTotalScoreView: View {
    
    @Binding var isPresented: Bool
    @Binding var selectedDateRange: DateRangeType
    
    let totalScore: Int
    let dateRangeText: String
    
    @State private var animationTrigger = false
    @State private var showDateRangePicker = false
    
    var body: some View {
        ZStack {
            // 半透明背景遮罩
            if isPresented {
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }
                    .transition(.opacity)
                
                // 主要内容卡片
                VStack(spacing: 0) {
                    // 顶部标题栏
                    HStack {
                        Text("全家总积分")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Spacer()
                        
                        Button(action: {
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                isPresented = false
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 24))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    }
                    .padding(.horizontal, 24)
                    .padding(.top, 24)
                    .padding(.bottom, 16)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                    
                    // 积分显示区域
                    VStack(spacing: 20) {
                        // 总积分显示
                        VStack(spacing: 8) {
                            Text("累计总分")
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("\(totalScore)")
                                .font(.system(size: 48, weight: .bold))
                                .foregroundColor(Color(hex: "#74c07f"))
                        }
                        .padding(.vertical, 20)
                        .frame(maxWidth: .infinity)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(hex: "#f8fff2"))
                        )
                        
                        // 时间范围选择
                        VStack(spacing: 12) {
                            HStack {
                                Text("统计时间范围")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                
                                Spacer()
                            }
                            
                            Button(action: {
                                showDateRangePicker = true
                            }) {
                                HStack {
                                    Image(systemName: "calendar")
                                        .font(.system(size: 16))
                                        .foregroundColor(Color(hex: "#74c07f"))
                                    
                                    Text(dateRangeText)
                                        .font(.system(size: 16))
                                        .foregroundColor(DesignSystem.Colors.textPrimary)
                                    
                                    Spacer()
                                    
                                    Image(systemName: "chevron.right")
                                        .font(.system(size: 14))
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }
                                .padding(.horizontal, 16)
                                .padding(.vertical, 12)
                                .background(
                                    RoundedRectangle(cornerRadius: 12)
                                        .fill(Color.white)
                                        .overlay(
                                            RoundedRectangle(cornerRadius: 12)
                                                .stroke(Color(hex: "#a9d051").opacity(0.3), lineWidth: 1)
                                        )
                                )
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                        
                        // 说明文字
                        VStack(spacing: 8) {
                            HStack {
                                Image(systemName: "info.circle")
                                    .font(.system(size: 14))
                                    .foregroundColor(Color(hex: "#74c07f"))
                                
                                Text("统计说明")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(DesignSystem.Colors.textPrimary)
                                
                                Spacer()
                            }
                            
                            VStack(alignment: .leading, spacing: 4) {
                                Text("• 显示所选时间范围内所有家庭成员的积分总和")
                                Text("• 包含加分和扣分记录")
                                Text("• 不包含抽奖消耗的积分")
                            }
                            .font(.system(size: 12))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .frame(maxWidth: .infinity, alignment: .leading)
                        }
                        .padding(.horizontal, 16)
                        .padding(.vertical, 12)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color(hex: "#f8fff2").opacity(0.5))
                        )
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 20)
                    
                    // 分隔线
                    Rectangle()
                        .fill(Color(hex: "#edf5d9"))
                        .frame(height: 1)
                        .padding(.horizontal, 24)
                    
                    // 底部按钮
                    Button(action: {
                        withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                            isPresented = false
                        }
                    }) {
                        Text("确定")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .frame(height: 44)
                            .background(
                                RoundedRectangle(cornerRadius: 12)
                                    .fill(
                                        LinearGradient(
                                            gradient: Gradient(colors: [
                                                Color(hex: "#a9d051"),
                                                Color(hex: "#74c07f")
                                            ]),
                                            startPoint: .topLeading,
                                            endPoint: .bottomTrailing
                                        )
                                    )
                            )
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 20)
                }
                .background(
                    RoundedRectangle(cornerRadius: 24)
                        .fill(Color.white)
                        .shadow(color: Color.black.opacity(0.15), radius: 20, x: 0, y: 10)
                )
                .padding(.horizontal, 30)
                .scaleEffect(animationTrigger ? 1.0 : 0.9)
                .opacity(animationTrigger ? 1.0 : 0.0)
            }
        }
        .overlay(
            // 日期范围选择器
            Group {
                if showDateRangePicker {
                    DateRangePickerView(
                        selectedDateRange: $selectedDateRange,
                        isPresented: $showDateRangePicker
                    )
                    .transition(.opacity)
                }
            }
        )
        .onChange(of: isPresented) { newValue in
            if newValue {
                withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                    animationTrigger = true
                }
            } else {
                animationTrigger = false
            }
        }
    }
}

// MARK: - 日期范围类型
enum DateRangeType {
    case thisWeek
    case thisMonth
    case custom(start: Date, end: Date)
    
    var displayText: String {
        switch self {
        case .thisWeek:
            return "本周"
        case .thisMonth:
            return "本月"
        case .custom(let start, let end):
            let formatter = DateFormatter()
            formatter.dateStyle = .short
            formatter.locale = Locale.current
            return "\(formatter.string(from: start)) - \(formatter.string(from: end))"
        }
    }
}



// MARK: - Preview
#Preview {
    ZStack {
        Color.gray.opacity(0.2)
            .ignoresSafeArea()
        
        FamilyTotalScoreView(
            isPresented: .constant(true),
            selectedDateRange: .constant(.thisMonth),
            totalScore: 1250,
            dateRangeText: "本月"
        )
    }
}
