//
//  FamilyMemberGridView.swift
//  ztt2
//
//  Created by AI Assistant on 2025/7/29.
//

import SwiftUI

/**
 * 家庭成员网格视图组件
 * 基于参考项目设计，保持完全一致的UI和交互
 */
struct FamilyMemberGridView: View {

    // 示例数据结构
    struct FamilyMember {
        let id: String
        let name: String
        let role: String
        let currentPoints: Int
    }
    
    let members: [FamilyMember]
    let isDeleteMode: Bool
    let hasFamilies: Bool // 是否有家庭
    let onMemberTapped: (FamilyMember) -> Void
    let onEnterDeleteMode: () -> Void
    let onExitDeleteMode: () -> Void
    let onDeleteRequested: (FamilyMember) -> Void
    let onCreateFamilyTapped: () -> Void // 创建家庭回调
    let onRefresh: () async -> Void // 下拉刷新回调
    
    // 网格列配置
    private let columns = [
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md),
        GridItem(.flexible(), spacing: DesignSystem.Spacing.md)
    ]
    
    var body: some View {
        ScrollView {
            if members.isEmpty {
                // 空状态视图
                VStack(spacing: DesignSystem.Spacing.lg) {
                    if !hasFamilies {
                        // 无家庭状态
                        VStack(spacing: DesignSystem.Spacing.md) {
                            Image(systemName: "house")
                                .font(.system(size: 60))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                            
                            Text("还没有创建家庭")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("点击下方按钮创建您的第一个家庭")
                                .font(.system(size: 14))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                                .multilineTextAlignment(.center)
                            
                            Button(action: onCreateFamilyTapped) {
                                HStack {
                                    Image(systemName: "plus")
                                    Text("创建家庭")
                                }
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 24)
                                .padding(.vertical, 12)
                                .background(DesignSystem.Colors.primary)
                                .cornerRadius(25)
                            }
                        }
                    } else {
                        // 有家庭但无成员状态
                        VStack(spacing: DesignSystem.Spacing.md) {
                            Image(systemName: "person.2")
                                .font(.system(size: 60))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                            
                            Text("还没有家庭成员")
                                .font(.system(size: 18, weight: .medium))
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text("点击右上角的"+"按钮添加家庭成员")
                                .font(.system(size: 14))
                                .foregroundColor(DesignSystem.Colors.textTertiary)
                                .multilineTextAlignment(.center)
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, 60)
            } else {
                LazyVGrid(columns: columns, spacing: DesignSystem.Spacing.md) {
                    ForEach(Array(members.enumerated()), id: \.element.id) { index, member in
                        FamilyMemberCardView(
                            memberName: member.name,
                            memberRole: member.role,
                            currentPoints: member.currentPoints,
                            memberIndex: index + 1,  // 从1开始编号
                            isDeleteMode: isDeleteMode,
                            onLongPress: {
                                onEnterDeleteMode()
                            },
                            onDeleteTapped: {
                                onDeleteRequested(member)
                            },
                            onCardTapped: {
                                if isDeleteMode {
                                    onExitDeleteMode()
                                } else {
                                    onMemberTapped(member)
                                }
                            }
                        )
                    }
                }
                .padding(.horizontal, DesignSystem.Spacing.pageHorizontal)
                .padding(.top, DesignSystem.Spacing.xs)
            }
        }
        .refreshable {
            // 执行下拉刷新
            await onRefresh()
        }
    }
}

// MARK: - Preview
#Preview {
    return VStack {
        // 空状态预览（无家庭）
        FamilyMemberGridView(
            members: [],
            isDeleteMode: false,
            hasFamilies: false,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name)")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name)")
            },
            onCreateFamilyTapped: {
                print("点击创建家庭")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("Empty State") {
    return VStack {
        // 空状态（有家庭但无成员）
        FamilyMemberGridView(
            members: [],
            isDeleteMode: false,
            hasFamilies: true,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name)")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name)")
            },
            onCreateFamilyTapped: {
                print("点击创建家庭")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}

#Preview("With Members") {
    // 创建示例家庭成员
    let sampleMembers = [
        FamilyMemberGridView.FamilyMember(
            id: "1",
            name: "爸爸",
            role: "father",
            currentPoints: 0
        ),
        FamilyMemberGridView.FamilyMember(
            id: "2",
            name: "妈妈",
            role: "mother",
            currentPoints: 0
        ),
        FamilyMemberGridView.FamilyMember(
            id: "3",
            name: "多多",
            role: "son",
            currentPoints: 10
        ),
        FamilyMemberGridView.FamilyMember(
            id: "4",
            name: "朵朵",
            role: "daughter",
            currentPoints: 5
        )
    ]

    return VStack {
        FamilyMemberGridView(
            members: sampleMembers,
            isDeleteMode: false,
            hasFamilies: true,
            onMemberTapped: { member in
                print("点击了家庭成员: \(member.name)")
            },
            onEnterDeleteMode: {
                print("进入删除模式")
            },
            onExitDeleteMode: {
                print("退出删除模式")
            },
            onDeleteRequested: { member in
                print("请求删除家庭成员: \(member.name)")
            },
            onCreateFamilyTapped: {
                print("点击创建家庭")
            },
            onRefresh: {
                print("下拉刷新")
            }
        )
    }
    .background(DesignSystem.Colors.background)
}
